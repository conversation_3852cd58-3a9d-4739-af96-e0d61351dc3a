import React, { useState, useEffect } from "react";
import {
  Table,
  Badge,
  Button,
  Select,
  Input,
  DatePicker,
  Heading,
  Text,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import Spinner from "../../components/shared/spinner";

// Status badge colors
const statusColors = {
  pending: "yellow",
  confirmed: "green",
  checked_in: "blue",
  checked_out: "purple",
  canceled: "gray", // Changed from red to gray (American spelling)
  no_show: "gray",
};

// Payment status badge colors
const paymentStatusColors = {
  not_paid: "red",
  awaiting_payment: "yellow",
  partially_paid: "orange",
  paid: "green",
  refunded: "purple",
  partially_refunded: "blue",
};

const BookingList = ({ hotelId = null }) => {
  const navigate = useNavigate();
  const [bookings, setBookings] = useState([]);
  const [hotels, setHotels] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  // Calculate pagination values
  const pageCount = Math.ceil(totalCount / pageSize);
  const canNextPage = currentPage < pageCount - 1;
  const canPreviousPage = currentPage > 0;

  // Filter states
  const [statusFilter, setStatusFilter] = useState("pending_confirmed");
  const [hotelFilter, setHotelFilter] = useState("all");
  const [guestNameFilter, setGuestNameFilter] = useState("");
  const [guestEmailFilter, setGuestEmailFilter] = useState("");
  const [fromDateFilter, setFromDateFilter] = useState(null);
  const [toDateFilter, setToDateFilter] = useState(null);

  // Initialize hotelFilter from hotelId prop on component mount
  useEffect(() => {
    if (hotelId) {
      console.log(`Setting initial hotel filter from hotelId prop: ${hotelId}`);
      setHotelFilter(hotelId);
    }
  }, []);

  // Update hotelFilter when hotelId prop changes
  useEffect(() => {
    if (hotelId) {
      console.log(`Updating hotel filter from hotelId prop: ${hotelId}`);
      setHotelFilter(hotelId);
    }
  }, [hotelId]);

  // Fetch hotels for the filter dropdown
  const fetchHotels = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels?limit=100`);

      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }

      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Failed to fetch hotels");
    }
  };

  // Fetch bookings
  const fetchBookings = async () => {
    try {
      setIsLoading(true);

      // Build query parameters
      const params = new URLSearchParams();

      // Always use hotelFilter from state, which is updated by the hotelId prop
      if (hotelFilter && hotelFilter !== "all") {
        console.log(`Adding hotel_id=${hotelFilter} to API request params`);
        params.append("hotel_id", hotelFilter);
      } else if (hotelId) {
        // Fallback to hotelId prop if hotelFilter is not set correctly
        console.log(
          `Using hotelId prop (${hotelId}) as fallback for API request params`
        );
        params.append("hotel_id", hotelId);
      }

      if (statusFilter) {
        if (statusFilter === "pending_confirmed") {
          // Special case for reserved and confirmed
          params.append("status", "pending");
          params.append("has_room_id", "both"); // Custom parameter to handle both with and without room_id
        } else if (statusFilter === "reserved") {
          // Pending without room_id (unallocated)
          params.append("status", "pending");
          params.append("has_room_id", "false");
        } else if (statusFilter === "confirmed") {
          // Either confirmed status OR pending with room_id (allocated)
          params.append("status", "pending");
          params.append("has_room_id", "true");
        } else if (statusFilter !== "all") {
          // Handle other statuses normally
          params.append("status", statusFilter);
        }
      }

      // Payment status filter removed

      if (guestNameFilter) {
        params.append("guest_name", guestNameFilter);
      }

      if (guestEmailFilter) {
        params.append("guest_email", guestEmailFilter);
      }

      if (fromDateFilter) {
        params.append("from_date", format(fromDateFilter, "yyyy-MM-dd"));
      }

      if (toDateFilter) {
        params.append("to_date", format(toDateFilter, "yyyy-MM-dd"));
      }

      // Pagination
      params.append("limit", pageSize.toString());
      params.append("offset", (currentPage * pageSize).toString());

      // Fetch data
      const url = `/admin/hotel-management/bookings?${params.toString()}`;
      console.log(`Fetching bookings with URL: ${url}`);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch bookings");
      }

      const data = await response.json();
      setBookings(data.bookings);
      setTotalCount(data.count);
    } catch (error) {
      console.error("Error fetching bookings:", error);
      toast.error("Failed to fetch bookings");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch hotels on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Trigger initial fetch when component mounts with hotelId
  useEffect(() => {
    if (hotelId) {
      console.log(
        `Component mounted with hotelId: ${hotelId}, triggering fetch`
      );
      // Short delay to ensure hotelFilter state is updated
      const timer = setTimeout(() => {
        fetchBookings();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, []);

  // Initial fetch and when filters change
  useEffect(() => {
    // Reset to first page when filters change
    if (currentPage !== 0) {
      setCurrentPage(0);
      return; // The page change will trigger another fetch
    }

    fetchBookings();
  }, [hotelId, hotelFilter, statusFilter, guestNameFilter, guestEmailFilter, fromDateFilter, toDateFilter, currentPage, pageSize]);

  // Apply filters
  const handleApplyFilters = () => {
    setCurrentPage(1); // Reset to first page
    fetchBookings();
  };

  // Reset filters
  const handleResetFilters = () => {
    setStatusFilter("pending_confirmed");
    // If hotelId is provided from URL, keep that filter
    setHotelFilter(hotelId || "all");
    setGuestNameFilter("");
    setGuestEmailFilter("");
    setFromDateFilter(null);
    setToDateFilter(null);
    setCurrentPage(1);
    fetchBookings();
  };

  // View booking details
  const handleViewBooking = (bookingId) => {
    navigate(`/hotel-management/bookings/${bookingId}`);
  };

  // Create new booking
  const handleCreateBooking = () => {
    navigate("/hotel-management/bookings/create");
  };

  // Format date
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);

      // Check if date is valid and not Jan 1, 1970 (Unix epoch)
      if (
        isNaN(date.getTime()) ||
        (date.getFullYear() === 1970 &&
          date.getMonth() === 0 &&
          date.getDate() === 1)
      ) {
        return "Not specified";
      }

      return format(date, "MMM dd, yyyy");
    } catch (error) {
      console.error("Error formatting date:", error, dateString);
      return "Invalid date";
    }
  };

  return (
    <div className="space-y-6">
      <Toaster />

      <div className="flex justify-between items-center">
        <Heading>Bookings</Heading>
        <div className="flex gap-2">
          <Button
            variant="secondary"
            onClick={() => navigate("/hotel-management/carts")}
          >
            View Pending Carts
          </Button>
          <Button onClick={handleCreateBooking}>Create Booking</Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-md shadow-sm space-y-4">
        <Text className="font-medium">Filters</Text>

        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <Select.Trigger>
                <Select.Value placeholder="Status" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Statuses</Select.Item>
                <Select.Item value="pending_confirmed">
                  Reserved & Confirmed
                </Select.Item>
                <Select.Item value="reserved">
                  Reserved (Unallocated)
                </Select.Item>
                <Select.Item value="confirmed">
                  Confirmed (Room Allocated)
                </Select.Item>
                <Select.Item value="checked_in">Checked In</Select.Item>
                <Select.Item value="checked_out">Checked Out</Select.Item>
                <Select.Item value="canceled">Canceled</Select.Item>
                <Select.Item value="no_show">No Show</Select.Item>
              </Select.Content>
            </Select>
          </div>

          <div>
            <Select
              value={hotelFilter}
              onValueChange={setHotelFilter}
              disabled={!!hotelId}
            >
              <Select.Trigger className={hotelId ? "bg-gray-100" : ""}>
                <Select.Value placeholder="Hotel" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Hotels</Select.Item>
                {hotels.map((hotel) => (
                  <Select.Item key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>

          <div>
            <Input
              placeholder="Guest Name"
              value={guestNameFilter}
              onChange={(e) => setGuestNameFilter(e.target.value)}
            />
          </div>

          <div>
            <Input
              placeholder="Customer Email"
              value={guestEmailFilter}
              onChange={(e) => setGuestEmailFilter(e.target.value)}
            />
          </div>

          <div>
            <DatePicker
              placeholder="From Date"
              value={fromDateFilter}
              onChange={setFromDateFilter}
            />
          </div>

          <div>
            <DatePicker
              placeholder="To Date"
              value={toDateFilter}
              onChange={setToDateFilter}
            />
          </div>
        </div>

        <div className="flex space-x-2">
          <Button onClick={handleApplyFilters}>Apply Filters</Button>
          <Button variant="secondary" onClick={handleResetFilters}>
            Reset
          </Button>
        </div>
      </div>

      {/* Bookings Table */}
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Spinner size="medium" />
          <div className="ml-4">Loading bookings...</div>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-md shadow-sm overflow-hidden">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Booking ID</Table.HeaderCell>
                  <Table.HeaderCell>Guest</Table.HeaderCell>
                  <Table.HeaderCell>Customer Email</Table.HeaderCell>
                  <Table.HeaderCell>Hotel</Table.HeaderCell>
                  <Table.HeaderCell>Room Type</Table.HeaderCell>
                  <Table.HeaderCell>Check-in</Table.HeaderCell>
                  <Table.HeaderCell>Check-out</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Total</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {bookings.length === 0 ? (
                  <Table.Row>
                    <Table.Cell colSpan={9} className="text-center py-4">
                      No bookings found
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  bookings.map((booking) => (
                    <Table.Row
                      key={booking.order_id}
                      className="cursor-pointer hover:bg-gray-50 transition-colors"
                      onClick={() => handleViewBooking(booking.order_id)}
                    >
                      <Table.Cell className="font-medium">
                        {booking.order_id}
                      </Table.Cell>
                      <Table.Cell>
                        {booking.metadata?.guest_name ||
                          booking.guest_name ||
                          "Guest"}
                      </Table.Cell>
                      <Table.Cell className="max-w-48 truncate" title={booking.guest_email}>
                        {booking.guest_email || "No email"}
                      </Table.Cell>
                      <Table.Cell>
                        {booking.metadata?.hotel_name ||
                          (booking.metadata?.hotel_id
                            ? hotels.find(
                                (h) => h.id === booking.metadata?.hotel_id
                              )?.name
                            : "Not specified")}
                      </Table.Cell>
                      <Table.Cell>
                        {booking.metadata?.room_type ||
                          booking.room_type ||
                          "Standard"}
                      </Table.Cell>
                      <Table.Cell>
                        {formatDate(
                          booking.metadata?.check_in_date ||
                            booking.check_in_date
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {formatDate(
                          booking.metadata?.check_out_date ||
                            booking.check_out_date
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {booking.status === "pending" ? (
                          booking.metadata?.room_id ? (
                            <Badge color="green">Confirmed </Badge>
                          ) : (
                            <Badge color="yellow">Reserved </Badge>
                          )
                        ) : (
                          <Badge color={statusColors[booking.status] || "gray"}>
                            {booking.status}
                          </Badge>
                        )}
                      </Table.Cell>
                      <Table.Cell>
                        {new Intl.NumberFormat("en-US", {
                          style: "currency",
                          currency:
                            booking.metadata?.currency_code ||
                            booking.currency_code ||
                            "USD",
                        }).format(
                          booking.metadata?.total_amount ||
                            booking.total_amount ||
                            0
                        )}
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>

          {/* Pagination */}
          <Table.Pagination
            count={totalCount}
            pageSize={pageSize}
            pageIndex={currentPage}
            pageCount={pageCount}
            canPreviousPage={canPreviousPage}
            canNextPage={canNextPage}
            previousPage={() => setCurrentPage(currentPage - 1)}
            nextPage={() => setCurrentPage(currentPage + 1)}
          />
        </>
      )}
    </div>
  );
};

export default BookingList;
